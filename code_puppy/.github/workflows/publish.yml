# Publishes your package to PyPI using Astral/uv for builds.
# Set PYPI_API_TOKEN in repo secrets.

permissions:
  contents: write # Allows writing to the repository

name: Build and Publish to Py<PERSON>

on:
  push:
    branches:
      - main

jobs:
  build-publish:
    runs-on: ubuntu-latest
    permissions:
      contents: write # Allows writing to the repository
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install uv, build, and twine
        run: pip install uv build twine

      - name: Setup uv virtual environment
        run: uv venv

      - name: Bump version
        run: uv version --bump patch

      - name: Build package
        run: |
          uv build

      - name: Publish to PyPI
        env:
          TWINE_USERNAME: __token__
          TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
        run: uv run twine upload dist/*

      - name: Push version bump to GitHub
        if: ${{ success() }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add .
          git commit -m "chore: bump version [ci skip]" || echo "No changes to commit"
          git push
