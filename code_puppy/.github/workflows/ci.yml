name: Quality Checks

on:
  pull_request:
    branches:
      - '**'

jobs:
  quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install dev dependencies (ruff, pytest)
        run: pip install ruff pytest pytest-cov pytest-asyncio

      - name: Install code_puppy
        run: pip install .

      - name: Lint with ruff
        run: ruff check .

      - name: Check formatting with ruff
        run: ruff format --check .

      - name: Run pytest
        run: pytest --cov=code_puppy -s
