[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "code-puppy"
version = "0.0.73"
description = "Code generation agent"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "pydantic-ai>=0.3.2",
    "httpx>=0.24.1",
    "rich>=13.4.2",
    "logfire>=0.7.1",
    "pydantic>=2.4.0",
    "python-dotenv>=1.0.0",
    "bs4>=0.0.2",
    "pytest-cov>=6.1.1",
    "ruff>=0.11.11",
    "httpx-limiter>=0.3.0",
    "prompt-toolkit>=3.0.38",
    "pathspec>=0.11.0",
    "rapidfuzz>=3.13.0",
    "json-repair>=0.46.2",
    "tree-sitter-language-pack>=0.8.0",
    "tree-sitter-typescript>=0.23.2",
]
dev-dependencies = [
    "pytest>=8.3.4",
    "pytest-cov>=6.1.1",
    "pytest-asyncio>=0.23.1",
    "ruff>=0.11.11",
]
authors = [
    {name = "<PERSON> Pfaffenberger"}
]
license = {text = "MIT"}
classifiers = [
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Topic :: Software Development :: Code Generators",
]

[project.scripts]
code-puppy = "code_puppy.main:main_entry"

[tool.logfire]
ignore_no_config = true

[tool.hatch.build]
packages = ["code_puppy"]
build_data = true

[tool.hatch.build.targets.wheel.shared-data]
"code_puppy/models.json" = "code_puppy/models.json"

[[tool.hatch.build.targets.sdist.include]]
path = "code_puppy/models.json"
[tool.pytest.ini_options]
addopts = "--cov=code_puppy --cov-report=term-missing"
testpaths = ["tests"]

[tool.coverage.run]
omit = ["code_puppy/main.py"]
