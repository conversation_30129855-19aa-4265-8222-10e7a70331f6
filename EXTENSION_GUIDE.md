# Code Puppy VSCode Extension - Development Guide

## Overview

This VSCode extension integrates Code Puppy directly into your development environment, providing AI-powered code generation and chat capabilities similar to Augment.

## Architecture

The extension is built with the following components:

### Core Components

1. **Extension Entry Point** (`src/extension.ts`)
   - Handles activation/deactivation
   - Initializes all services
   - Manages extension lifecycle

2. **Configuration Manager** (`src/config/ConfigManager.ts`)
   - Reads/writes `~/.code_puppy/puppy.cfg`
   - Manages MCP servers configuration
   - Handles VSCode settings integration

3. **Python Process Manager** (`src/services/PuppyProcessManager.ts`)
   - Spawns and manages Code Puppy Python processes
   - Handles both interactive and single-command modes
   - Auto-detects local vs global installations
   - Manages virtual environment integration

4. **Chat Interface** (`src/webview/ChatPanel.ts`)
   - Provides webview-based chat UI
   - Handles real-time communication with Python process
   - Supports meta commands and message history

5. **Configuration UI** (`src/webview/ConfigPanel.ts`)
   - GUI for managing Code Puppy settings
   - MCP servers configuration interface
   - Visual configuration management

6. **Command Handler** (`src/commands/CommandHandler.ts`)
   - Implements all VSCode commands
   - Handles file context integration
   - Manages command palette actions

7. **Status Bar Manager** (`src/ui/StatusBarManager.ts`)
   - Shows current model and puppy status
   - Provides quick access to common functions
   - Visual feedback for extension state

## Features Implemented

### ✅ Core Features
- **Interactive Chat Interface** - Full-featured chat with Code Puppy
- **Configuration Management** - Read/write Code Puppy configs
- **Meta Commands Support** - All meta commands (~show, ~set, ~m, etc.)
- **File Context Integration** - Send code selections to chat
- **Status Bar Integration** - Model and status display
- **Local Installation Support** - Auto-detects workspace installations
- **Virtual Environment Support** - Uses venv when available

### ✅ Commands Available
- `Code Puppy: Open Chat` - Open chat interface
- `Code Puppy: Send to Chat` - Send selected code to chat
- `Code Puppy: Show Configuration` - Display current config
- `Code Puppy: Set Configuration` - Set config values
- `Code Puppy: Switch Model` - Change AI model
- `Code Puppy: Clear Chat History` - Clear conversation
- `Code Puppy: Show Code Map` - Generate code structure
- `Code Puppy: Open Configuration UI` - Open config panel

### ✅ Configuration Options
- `codePuppy.pythonPath` - Python executable path
- `codePuppy.codePuppyPath` - Code Puppy installation path
- `codePuppy.autoShowChat` - Auto-show chat on startup
- `codePuppy.messageHistoryLimit` - Chat history limit
- `codePuppy.enableStatusBar` - Show status bar items

## Installation & Setup

### Prerequisites
1. **Code Puppy Dependencies**: The extension automatically detects and uses the local Code Puppy installation in your workspace
2. **Virtual Environment**: A virtual environment has been created at `code_puppy/venv/` with all dependencies installed

### Extension Installation
1. The extension is ready to use in development mode
2. Press `F5` in VSCode to launch Extension Development Host
3. Test the extension in the new window

### First-Time Setup
1. The extension will prompt for puppy name and owner name
2. Configuration is stored in `~/.code_puppy/puppy.cfg`
3. Chat interface opens automatically (configurable)

## Usage

### Chat Interface
- Open with Command Palette → "Code Puppy: Open Chat"
- Type coding questions or requests
- Use meta commands: `~show`, `~set model=gpt-4o`, `~clear`
- Send code by right-clicking and selecting "Send to Code Puppy"

### Configuration
- Use "Code Puppy: Open Configuration UI" for GUI management
- Or use meta commands in chat: `~set key=value`
- Configuration files are in `~/.code_puppy/`

### Status Bar
- 🐶 **Puppy Name** - Click to open chat
- 🔧 **Current Model** - Click to switch models

## Development

### Building
```bash
npm install
npm run compile
```

### Testing
```bash
# Launch Extension Development Host
# Press F5 in VSCode

# Or run tests
npm test
```

### File Structure
```
src/
├── extension.ts              # Main entry point
├── types/index.ts           # TypeScript interfaces
├── config/
│   └── ConfigManager.ts     # Configuration management
├── services/
│   └── PuppyProcessManager.ts # Python process handling
├── webview/
│   ├── ChatPanel.ts         # Chat interface
│   └── ConfigPanel.ts       # Configuration UI
├── commands/
│   └── CommandHandler.ts    # Command implementations
└── ui/
    └── StatusBarManager.ts  # Status bar integration
```

## Troubleshooting

### Common Issues

1. **"Code Puppy not found"**
   - Ensure the virtual environment is activated
   - Check that dependencies are installed in `code_puppy/venv/`
   - Verify Python path in settings

2. **Chat not responding**
   - Check Output panel (View → Output → Code Puppy)
   - Verify Code Puppy works in terminal: `source code_puppy/venv/bin/activate && python -m code_puppy.main --help`

3. **Configuration issues**
   - Check `~/.code_puppy/puppy.cfg` exists
   - Use "Code Puppy: Show Configuration" to debug
   - Ensure puppy_name and owner_name are set

### Debug Information
- Output Channel: View → Output → Code Puppy
- Configuration paths shown in Configuration UI
- Extension logs in VSCode Developer Console

## Next Steps

The extension is fully functional with all core features implemented. Potential enhancements:

1. **Enhanced Error Handling** - Better error messages and recovery
2. **Syntax Highlighting** - Code highlighting in chat responses
3. **File Diff Integration** - Apply code changes directly to files
4. **Model Management** - Dynamic model loading from models.json
5. **MCP Server Management** - GUI for adding/removing MCP servers
6. **Testing Suite** - Comprehensive unit and integration tests

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test thoroughly
4. Submit a pull request

The extension provides a solid foundation for Code Puppy integration in VSCode and can be extended with additional features as needed.
