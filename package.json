{"name": "code-puppy", "displayName": "Code Puppy", "description": "VSCode extension for Code Puppy - AI-powered code generation and chat", "version": "0.0.1", "publisher": "code-puppy", "engines": {"vscode": "^1.101.0"}, "categories": ["AI", "Cha<PERSON>", "Other"], "keywords": ["ai", "chat", "code generation", "assistant", "puppy"], "activationEvents": ["onStartupFinished"], "main": "./dist/extension.js", "contributes": {"commands": [{"command": "code-puppy.openChat", "title": "Open Chat", "category": "Code Puppy", "icon": "$(comment-discussion)"}, {"command": "code-puppy.sendToChat", "title": "Send to Code Puppy", "category": "Code Puppy", "icon": "$(send)"}, {"command": "code-puppy.showConfig", "title": "Show Configuration", "category": "Code Puppy", "icon": "$(gear)"}, {"command": "code-puppy.setConfig", "title": "Set Configuration", "category": "Code Puppy"}, {"command": "code-puppy.switchModel", "title": "Switch Model", "category": "Code Puppy", "icon": "$(symbol-class)"}, {"command": "code-puppy.clearHistory", "title": "Clear Chat History", "category": "Code Puppy", "icon": "$(clear-all)"}, {"command": "code-puppy.showCodeMap", "title": "Show Code Map", "category": "Code Puppy", "icon": "$(symbol-structure)"}, {"command": "code-puppy.openConfigUI", "title": "Open Configuration UI", "category": "Code Puppy", "icon": "$(settings-gear)"}], "menus": {"editor/context": [{"command": "code-puppy.sendToChat", "when": "editorHasSelection", "group": "code-puppy"}], "explorer/context": [{"command": "code-puppy.showCodeMap", "when": "explorerResourceIsFolder", "group": "code-puppy"}], "commandPalette": [{"command": "code-puppy.sendToChat", "when": "editorHasSelection"}]}, "configuration": {"title": "Code Puppy", "properties": {"codePuppy.pythonPath": {"type": "string", "default": "python", "description": "Path to Python executable for running Code Puppy"}, "codePuppy.codePuppyPath": {"type": "string", "default": "", "description": "Path to Code Puppy installation (leave empty for auto-detection)"}, "codePuppy.autoShowChat": {"type": "boolean", "default": true, "description": "Automatically show chat panel when extension activates"}, "codePuppy.messageHistoryLimit": {"type": "number", "default": 40, "description": "Number of messages to keep in chat history"}, "codePuppy.enableStatusBar": {"type": "boolean", "default": true, "description": "Show Code Puppy status in status bar"}}}}, "scripts": {"vscode:prepublish": "yarn run package", "compile": "yarn run check-types && yarn run lint && node esbuild.js", "watch": "npm-run-all -p watch:*", "watch:esbuild": "node esbuild.js --watch", "watch:tsc": "tsc --noEmit --watch --project tsconfig.json", "package": "yarn run check-types && yarn run lint && node esbuild.js --production", "compile-tests": "tsc -p . --outDir out", "watch-tests": "tsc -p . -w --outDir out", "pretest": "yarn run compile-tests && yarn run compile && yarn run lint", "check-types": "tsc --noEmit", "lint": "eslint src", "test": "vscode-test"}, "devDependencies": {"@types/vscode": "^1.101.0", "@types/mocha": "^10.0.10", "@types/node": "20.x", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "eslint": "^9.25.1", "esbuild": "^0.25.3", "npm-run-all": "^4.1.5", "typescript": "^5.8.3", "@vscode/test-cli": "^0.0.11", "@vscode/test-electron": "^2.5.2"}}