# Code Puppy VSCode Extension

A VSCode extension that integrates [Code Puppy](https://github.com/mpf<PERSON><PERSON><PERSON>/code_puppy) directly into your development environment, providing AI-powered code generation and chat capabilities similar to Augment.

## Features

🐶 **Interactive Chat Interface** - Chat with Code Puppy directly in VSCode with a beautiful, responsive interface

⚙️ **Configuration Management** - Manage Code Puppy settings, models, and MCP servers through VSCode

🔧 **Meta Commands** - Full support for Code Puppy meta commands (~show, ~set, ~m, etc.)

📁 **File Context Integration** - Send selected code or entire files to Code Puppy for analysis

📊 **Status Bar Integration** - See current model and puppy status at a glance

🗺️ **Code Mapping** - Generate code structure maps for directories

## Requirements

- **Code Puppy Python Package**: Install with `pip install code_puppy`
- **Python 3.8+**: Required for running Code Puppy
- **VSCode 1.101.0+**: Required for the extension

## Installation

1. **Install Code Puppy Python Package**
   ```bash
   pip install code_puppy
   ```

2. **Install the VSCode Extension**
   - Open VSCode
   - Go to Extensions (Ctrl+Shift+P → "Extensions: Install Extensions")
   - Search for "Code Puppy"
   - Click Install

## Quick Start

1. **First Launch**: The extension will prompt you to set up your puppy name and owner name
2. **Open Chat**: Use Command Palette (Ctrl+Shift+P) → "Code Puppy: Open Chat"
3. **Start Chatting**: Type your coding questions or use meta commands like `~help`

## Extension Settings

This extension contributes the following settings:

* `codePuppy.pythonPath`: Path to Python executable for running Code Puppy
* `codePuppy.codePuppyPath`: Path to Code Puppy installation (leave empty for auto-detection)
* `codePuppy.autoShowChat`: Automatically show chat panel when extension activates
* `codePuppy.messageHistoryLimit`: Number of messages to keep in chat history
* `codePuppy.enableStatusBar`: Show Code Puppy status in status bar

## Commands

| Command | Description |
|---------|-------------|
| `Code Puppy: Open Chat` | Open the chat interface |
| `Code Puppy: Send to Chat` | Send selected code to chat |
| `Code Puppy: Show Configuration` | Display current configuration |
| `Code Puppy: Switch Model` | Change the AI model |
| `Code Puppy: Open Configuration UI` | Open configuration panel |

## Usage

### Chat Interface
- Type coding questions or requests
- Use meta commands: `~show`, `~set model=gpt-4o`, `~clear`
- Send code by right-clicking and selecting "Send to Code Puppy"

### Status Bar
- 🐶 **Puppy Name** - Click to open chat
- 🔧 **Current Model** - Click to switch models

## Known Issues

- First-time setup requires manual configuration of puppy name and owner
- Large file processing may take time depending on the model
- MCP server configuration requires manual JSON editing

## Release Notes

### 0.0.1

Initial release with core functionality:
- Interactive chat interface
- Configuration management
- Meta commands support
- File context integration
- Status bar integration

---

**Enjoy coding with your AI puppy! 🐶**
