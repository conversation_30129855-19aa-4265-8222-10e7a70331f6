import * as vscode from 'vscode';
import { ConfigManager } from '../config/ConfigManager';

export class StatusBarManager {
    private static instance: StatusBarManager;
    private statusBarItem: vscode.StatusBarItem;
    private modelStatusBarItem: vscode.StatusBarItem;
    private configManager: ConfigManager;

    private constructor() {
        this.configManager = ConfigManager.getInstance();
        
        // Main Code Puppy status bar item
        this.statusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Left,
            100
        );
        this.statusBarItem.command = 'code-puppy.openChat';
        this.statusBarItem.tooltip = 'Click to open Code Puppy chat';

        // Model status bar item
        this.modelStatusBarItem = vscode.window.createStatusBarItem(
            vscode.StatusBarAlignment.Left,
            99
        );
        this.modelStatusBarItem.command = 'code-puppy.switchModel';
        this.modelStatusBarItem.tooltip = 'Click to switch model';
    }

    public static getInstance(): StatusBarManager {
        if (!StatusBarManager.instance) {
            StatusBarManager.instance = new StatusBarManager();
        }
        return StatusBarManager.instance;
    }

    /**
     * Initialize and show status bar items
     */
    public async initialize(): Promise<void> {
        const config = vscode.workspace.getConfiguration('codePuppy');
        const enableStatusBar = config.get<boolean>('enableStatusBar', true);

        if (enableStatusBar) {
            await this.updateStatusBar();
            this.statusBarItem.show();
            this.modelStatusBarItem.show();
        }
    }

    /**
     * Update status bar with current information
     */
    public async updateStatusBar(): Promise<void> {
        try {
            const puppyName = await this.configManager.getPuppyName();
            const currentModel = await this.configManager.getCurrentModel();

            // Update main status bar item
            this.statusBarItem.text = `🐶 ${puppyName}`;

            // Update model status bar item
            this.modelStatusBarItem.text = `$(symbol-class) ${currentModel}`;

        } catch (error) {
            console.error('Error updating status bar:', error);
            this.statusBarItem.text = '🐶 Code Puppy';
            this.modelStatusBarItem.text = '$(symbol-class) Unknown';
        }
    }

    /**
     * Show status bar items
     */
    public show(): void {
        const config = vscode.workspace.getConfiguration('codePuppy');
        const enableStatusBar = config.get<boolean>('enableStatusBar', true);

        if (enableStatusBar) {
            this.statusBarItem.show();
            this.modelStatusBarItem.show();
        }
    }

    /**
     * Hide status bar items
     */
    public hide(): void {
        this.statusBarItem.hide();
        this.modelStatusBarItem.hide();
    }

    /**
     * Update status when model changes
     */
    public async onModelChanged(newModel: string): Promise<void> {
        this.modelStatusBarItem.text = `$(symbol-class) ${newModel}`;
    }

    /**
     * Update status when puppy name changes
     */
    public async onPuppyNameChanged(newName: string): Promise<void> {
        this.statusBarItem.text = `🐶 ${newName}`;
    }

    /**
     * Show busy indicator
     */
    public showBusy(message: string = 'Processing...'): void {
        this.statusBarItem.text = `🐶 $(loading~spin) ${message}`;
    }

    /**
     * Hide busy indicator and restore normal status
     */
    public async hideBusy(): Promise<void> {
        await this.updateStatusBar();
    }

    /**
     * Show error state
     */
    public showError(message: string = 'Error'): void {
        this.statusBarItem.text = `🐶 $(error) ${message}`;
        this.statusBarItem.tooltip = `Code Puppy Error: ${message}`;
    }

    /**
     * Dispose of status bar items
     */
    public dispose(): void {
        this.statusBarItem.dispose();
        this.modelStatusBarItem.dispose();
    }
}
