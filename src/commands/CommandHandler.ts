import * as vscode from 'vscode';
import { ChatPanel } from '../webview/ChatPanel';
import { ConfigPanel } from '../webview/ConfigPanel';
import { ConfigManager } from '../config/ConfigManager';
import { PuppyProcessManager } from '../services/PuppyProcessManager';

export class CommandHandler {
    private configManager: ConfigManager;
    private processManager: PuppyProcessManager;

    constructor() {
        this.configManager = ConfigManager.getInstance();
        this.processManager = PuppyProcessManager.getInstance();
    }

    /**
     * Register all commands
     */
    public registerCommands(context: vscode.ExtensionContext): void {
        const commands = [
            vscode.commands.registerCommand('code-puppy.openChat', () => this.openChat(context)),
            vscode.commands.registerCommand('code-puppy.sendToChat', () => this.sendToChat(context)),
            vscode.commands.registerCommand('code-puppy.showConfig', () => this.showConfig()),
            vscode.commands.registerCommand('code-puppy.setConfig', () => this.setConfig()),
            vscode.commands.registerCommand('code-puppy.switchModel', () => this.switchModel()),
            vscode.commands.registerCommand('code-puppy.clearHistory', () => this.clearHistory()),
            vscode.commands.registerCommand('code-puppy.showCodeMap', (uri) => this.showCodeMap(uri)),
            vscode.commands.registerCommand('code-puppy.openConfigUI', () => this.openConfigUI(context))
        ];

        commands.forEach(command => context.subscriptions.push(command));
    }

    /**
     * Open chat panel
     */
    private openChat(context: vscode.ExtensionContext): void {
        ChatPanel.createOrShow(context.extensionUri);
    }

    /**
     * Send selected text or current file to chat
     */
    private async sendToChat(context: vscode.ExtensionContext): Promise<void> {
        const editor = vscode.window.activeTextEditor;
        if (!editor) {
            vscode.window.showWarningMessage('No active editor found');
            return;
        }

        let content = '';
        const selection = editor.selection;
        
        if (!selection.isEmpty) {
            // Send selected text
            content = editor.document.getText(selection);
        } else {
            // Send entire file with context
            const fileName = editor.document.fileName;
            const fileContent = editor.document.getText();
            content = `File: ${fileName}\n\n${fileContent}`;
        }

        // Open chat panel if not already open
        ChatPanel.createOrShow(context.extensionUri);

        // Add the content as a message
        if (ChatPanel.currentPanel) {
            ChatPanel.currentPanel.addMessage({
                id: Date.now().toString(),
                role: 'user',
                content: `Please analyze this code:\n\n${content}`,
                timestamp: new Date()
            });
        }
    }

    /**
     * Show current configuration
     */
    private async showConfig(): Promise<void> {
        try {
            const config = await this.configManager.getAllConfig();
            const puppyName = await this.configManager.getPuppyName();
            const ownerName = await this.configManager.getOwnerName();
            const model = await this.configManager.getCurrentModel();
            const historyLimit = await this.configManager.getMessageHistoryLimit();

            const configText = `🐶 Code Puppy Configuration

Puppy Name: ${puppyName}
Owner: ${ownerName}
Current Model: ${model}
Message History Limit: ${historyLimit}

All Configuration:
${JSON.stringify(config, null, 2)}`;

            const document = await vscode.workspace.openTextDocument({
                content: configText,
                language: 'json'
            });
            
            await vscode.window.showTextDocument(document);

        } catch (error) {
            vscode.window.showErrorMessage(`Error reading config: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Set configuration value
     */
    private async setConfig(): Promise<void> {
        try {
            const key = await vscode.window.showInputBox({
                prompt: 'Enter configuration key',
                placeHolder: 'e.g., model, puppy_name, owner_name'
            });

            if (!key) {
                return;
            }

            const value = await vscode.window.showInputBox({
                prompt: `Enter value for ${key}`,
                placeHolder: 'Configuration value'
            });

            if (value === undefined) {
                return;
            }

            await this.configManager.setConfigValue(key, value);
            vscode.window.showInformationMessage(`Set ${key} = "${value}"`);

        } catch (error) {
            vscode.window.showErrorMessage(`Error setting config: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Switch model
     */
    private async switchModel(): Promise<void> {
        try {
            // Common model options - could be loaded from models.json in the future
            const models = [
                'gpt-4.1',
                'gpt-4o',
                'gpt-3.5-turbo',
                'claude-3-sonnet',
                'claude-3-haiku',
                'gemini-pro'
            ];

            const currentModel = await this.configManager.getCurrentModel();
            
            const selectedModel = await vscode.window.showQuickPick(models, {
                placeHolder: `Current model: ${currentModel}. Select a new model:`,
                canPickMany: false
            });

            if (selectedModel) {
                await this.configManager.setConfigValue('model', selectedModel);
                vscode.window.showInformationMessage(`Switched to model: ${selectedModel}`);
            }

        } catch (error) {
            vscode.window.showErrorMessage(`Error switching model: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Clear chat history
     */
    private clearHistory(): void {
        if (ChatPanel.currentPanel) {
            ChatPanel.currentPanel.clearMessages();
            vscode.window.showInformationMessage('Chat history cleared');
        } else {
            vscode.window.showWarningMessage('No active chat panel found');
        }
    }

    /**
     * Show code map for directory
     */
    private async showCodeMap(uri?: vscode.Uri): Promise<void> {
        try {
            let targetPath: string;

            if (uri) {
                targetPath = uri.fsPath;
            } else {
                const workspaceFolders = vscode.workspace.workspaceFolders;
                if (workspaceFolders && workspaceFolders.length > 0) {
                    targetPath = workspaceFolders[0].uri.fsPath;
                } else {
                    vscode.window.showWarningMessage('No workspace folder found');
                    return;
                }
            }

            // Execute codemap command
            const output = await this.processManager.executeCommand(`~codemap "${targetPath}"`);
            
            const document = await vscode.workspace.openTextDocument({
                content: output,
                language: 'plaintext'
            });
            
            await vscode.window.showTextDocument(document);

        } catch (error) {
            vscode.window.showErrorMessage(`Error generating code map: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Open configuration UI
     */
    private openConfigUI(context: vscode.ExtensionContext): void {
        ConfigPanel.createOrShow(context.extensionUri);
    }

    /**
     * Initialize configuration if needed
     */
    public async initializeIfNeeded(): Promise<void> {
        try {
            const isInitialized = await this.configManager.isConfigInitialized();
            
            if (!isInitialized) {
                const result = await vscode.window.showInformationMessage(
                    '🐾 Welcome to Code Puppy! Let\'s set up your configuration.',
                    'Setup Now',
                    'Later'
                );

                if (result === 'Setup Now') {
                    await this.configManager.initializeConfig();
                    vscode.window.showInformationMessage('🐶 Code Puppy is ready to help!');
                }
            }
        } catch (error) {
            vscode.window.showErrorMessage(`Error initializing config: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Check if Code Puppy is available
     */
    public async checkAvailability(): Promise<boolean> {
        try {
            const isAvailable = await this.processManager.checkAvailability();
            
            if (!isAvailable) {
                const result = await vscode.window.showWarningMessage(
                    'Code Puppy is not available. Please ensure it is installed and accessible.',
                    'Open Output',
                    'Settings'
                );

                if (result === 'Open Output') {
                    this.processManager.getOutputChannel().show();
                } else if (result === 'Settings') {
                    vscode.commands.executeCommand('workbench.action.openSettings', 'codePuppy');
                }
                
                return false;
            }

            return true;
        } catch (error) {
            vscode.window.showErrorMessage(`Error checking Code Puppy availability: ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }
}
