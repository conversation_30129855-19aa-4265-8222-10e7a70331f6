import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as vscode from 'vscode';
import { PuppyConfig, MCPServersConfig } from '../types';

export class ConfigManager {
    private static instance: ConfigManager;
    private configDir: string;
    private configFile: string;
    private mcpServersFile: string;

    private constructor() {
        this.configDir = path.join(os.homedir(), '.code_puppy');
        this.configFile = path.join(this.configDir, 'puppy.cfg');
        this.mcpServersFile = path.join(this.configDir, 'mcp_servers.json');
    }

    public static getInstance(): ConfigManager {
        if (!ConfigManager.instance) {
            ConfigManager.instance = new ConfigManager();
        }
        return ConfigManager.instance;
    }

    /**
     * Ensure the config directory exists
     */
    private ensureConfigDir(): void {
        if (!fs.existsSync(this.configDir)) {
            fs.mkdirSync(this.configDir, { recursive: true });
        }
    }

    /**
     * Parse INI-style config file
     */
    private parseConfigFile(content: string): PuppyConfig {
        const config: PuppyConfig = {};
        const lines = content.split('\n');
        let inPuppySection = false;

        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed === '[puppy]') {
                inPuppySection = true;
                continue;
            }
            if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
                inPuppySection = false;
                continue;
            }
            if (inPuppySection && trimmed.includes('=')) {
                const [key, ...valueParts] = trimmed.split('=');
                const value = valueParts.join('=').trim();
                config[key.trim()] = value;
            }
        }

        return config;
    }

    /**
     * Convert config object to INI format
     */
    private configToIni(config: PuppyConfig): string {
        let content = '[puppy]\n';
        for (const [key, value] of Object.entries(config)) {
            if (value !== undefined) {
                content += `${key} = ${value}\n`;
            }
        }
        return content;
    }

    /**
     * Read the puppy configuration
     */
    public async readConfig(): Promise<PuppyConfig> {
        try {
            if (!fs.existsSync(this.configFile)) {
                return {};
            }
            const content = fs.readFileSync(this.configFile, 'utf8');
            return this.parseConfigFile(content);
        } catch (error) {
            console.error('Error reading config:', error);
            return {};
        }
    }

    /**
     * Write the puppy configuration
     */
    public async writeConfig(config: PuppyConfig): Promise<void> {
        try {
            this.ensureConfigDir();
            const content = this.configToIni(config);
            fs.writeFileSync(this.configFile, content, 'utf8');
        } catch (error) {
            console.error('Error writing config:', error);
            throw error;
        }
    }

    /**
     * Set a single config value
     */
    public async setConfigValue(key: string, value: string): Promise<void> {
        const config = await this.readConfig();
        config[key] = value;
        await this.writeConfig(config);
    }

    /**
     * Get a single config value
     */
    public async getConfigValue(key: string, defaultValue?: string): Promise<string | undefined> {
        const config = await this.readConfig();
        return config[key] || defaultValue;
    }

    /**
     * Read MCP servers configuration
     */
    public async readMCPServers(): Promise<MCPServersConfig> {
        try {
            if (!fs.existsSync(this.mcpServersFile)) {
                return { mcp_servers: {} };
            }
            const content = fs.readFileSync(this.mcpServersFile, 'utf8');
            return JSON.parse(content);
        } catch (error) {
            console.error('Error reading MCP servers config:', error);
            return { mcp_servers: {} };
        }
    }

    /**
     * Write MCP servers configuration
     */
    public async writeMCPServers(config: MCPServersConfig): Promise<void> {
        try {
            this.ensureConfigDir();
            const content = JSON.stringify(config, null, 2);
            fs.writeFileSync(this.mcpServersFile, content, 'utf8');
        } catch (error) {
            console.error('Error writing MCP servers config:', error);
            throw error;
        }
    }

    /**
     * Get puppy name from config
     */
    public async getPuppyName(): Promise<string> {
        return await this.getConfigValue('puppy_name', 'Puppy') || 'Puppy';
    }

    /**
     * Get owner name from config
     */
    public async getOwnerName(): Promise<string> {
        return await this.getConfigValue('owner_name', 'Master') || 'Master';
    }

    /**
     * Get current model from config
     */
    public async getCurrentModel(): Promise<string> {
        return await this.getConfigValue('model', 'gpt-4.1') || 'gpt-4.1';
    }

    /**
     * Get message history limit
     */
    public async getMessageHistoryLimit(): Promise<number> {
        const value = await this.getConfigValue('message_history_limit', '40');
        try {
            return Math.max(1, parseInt(value || '40'));
        } catch {
            return 40;
        }
    }

    /**
     * Check if config is properly initialized
     */
    public async isConfigInitialized(): Promise<boolean> {
        const config = await this.readConfig();
        return !!(config.puppy_name && config.owner_name);
    }

    /**
     * Initialize config with user input
     */
    public async initializeConfig(): Promise<void> {
        const puppyName = await vscode.window.showInputBox({
            prompt: 'What should we name the puppy?',
            placeHolder: 'Enter puppy name'
        });

        if (!puppyName) {
            throw new Error('Puppy name is required');
        }

        const ownerName = await vscode.window.showInputBox({
            prompt: "What's your name (so Code Puppy knows its master)?",
            placeHolder: 'Enter your name'
        });

        if (!ownerName) {
            throw new Error('Owner name is required');
        }

        await this.writeConfig({
            puppy_name: puppyName,
            owner_name: ownerName
        });
    }

    /**
     * Get all config keys and values
     */
    public async getAllConfig(): Promise<PuppyConfig> {
        return await this.readConfig();
    }

    /**
     * Get config file paths for debugging
     */
    public getConfigPaths(): { configDir: string; configFile: string; mcpServersFile: string } {
        return {
            configDir: this.configDir,
            configFile: this.configFile,
            mcpServersFile: this.mcpServersFile
        };
    }
}
