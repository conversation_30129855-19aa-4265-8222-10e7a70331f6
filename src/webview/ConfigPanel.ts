import * as vscode from 'vscode';
import { ConfigManager } from '../config/ConfigManager';
import { PuppyConfig, MCPServersConfig } from '../types';

export class ConfigPanel {
    public static currentPanel: ConfigPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];
    private _configManager: ConfigManager;

    public static createOrShow(extensionUri: vscode.Uri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (ConfigPanel.currentPanel) {
            ConfigPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'codePuppyConfig',
            'Code Puppy Configuration',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media'),
                    vscode.Uri.joinPath(extensionUri, 'out', 'webview')
                ]
            }
        );

        ConfigPanel.currentPanel = new ConfigPanel(panel, extensionUri);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._configManager = ConfigManager.getInstance();

        this._update();
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        this._panel.webview.onDidReceiveMessage(
            async (message) => {
                switch (message.type) {
                    case 'loadConfig':
                        await this._loadConfig();
                        break;
                    case 'saveConfig':
                        await this._saveConfig(message.data);
                        break;
                    case 'saveMCPServers':
                        await this._saveMCPServers(message.data);
                        break;
                    default:
                        break;
                }
            },
            null,
            this._disposables
        );
    }

    private async _loadConfig() {
        try {
            const config = await this._configManager.getAllConfig();
            const mcpServers = await this._configManager.readMCPServers();
            const paths = this._configManager.getConfigPaths();

            this._panel.webview.postMessage({
                type: 'configLoaded',
                data: {
                    config,
                    mcpServers,
                    paths
                }
            });
        } catch (error) {
            vscode.window.showErrorMessage(`Error loading config: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private async _saveConfig(config: PuppyConfig) {
        try {
            await this._configManager.writeConfig(config);
            vscode.window.showInformationMessage('Configuration saved successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Error saving config: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private async _saveMCPServers(mcpServers: MCPServersConfig) {
        try {
            await this._configManager.writeMCPServers(mcpServers);
            vscode.window.showInformationMessage('MCP servers configuration saved successfully!');
        } catch (error) {
            vscode.window.showErrorMessage(`Error saving MCP servers: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    private async _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = this._getHtmlForWebview(webview);
        
        // Load initial config
        await this._loadConfig();
    }

    private _getHtmlForWebview(webview: vscode.Webview): string {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Puppy Configuration</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1, h2 {
            color: var(--vscode-foreground);
            border-bottom: 1px solid var(--vscode-panel-border);
            padding-bottom: 10px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: var(--vscode-foreground);
        }
        
        input, textarea, select {
            width: 100%;
            padding: 8px 12px;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-family: inherit;
            font-size: inherit;
            box-sizing: border-box;
        }
        
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
        }
        
        button {
            padding: 10px 20px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: inherit;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .info {
            background-color: var(--vscode-inputValidation-infoBackground);
            border: 1px solid var(--vscode-inputValidation-infoBorder);
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .path-info {
            font-family: var(--vscode-editor-font-family);
            font-size: 0.9em;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }
        
        textarea {
            min-height: 100px;
            font-family: var(--vscode-editor-font-family);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐶 Code Puppy Configuration</h1>
        
        <div class="section">
            <h2>Basic Settings</h2>
            <div class="info">
                Configure your Code Puppy's basic settings like name, owner, and model preferences.
            </div>
            
            <div class="form-group">
                <label for="puppyName">Puppy Name:</label>
                <input type="text" id="puppyName" placeholder="Enter puppy name">
            </div>
            
            <div class="form-group">
                <label for="ownerName">Owner Name:</label>
                <input type="text" id="ownerName" placeholder="Enter your name">
            </div>
            
            <div class="form-group">
                <label for="model">Default Model:</label>
                <select id="model">
                    <option value="gpt-4.1">GPT-4.1</option>
                    <option value="gpt-4o">GPT-4o</option>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="claude-3-sonnet">Claude 3 Sonnet</option>
                    <option value="claude-3-haiku">Claude 3 Haiku</option>
                    <option value="gemini-pro">Gemini Pro</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="messageHistoryLimit">Message History Limit:</label>
                <input type="number" id="messageHistoryLimit" min="1" max="1000" placeholder="40">
            </div>
            
            <div class="form-group">
                <label for="yoloMode">YOLO Mode:</label>
                <select id="yoloMode">
                    <option value="">Disabled</option>
                    <option value="true">Enabled</option>
                </select>
            </div>
            
            <button onclick="saveBasicConfig()">Save Basic Settings</button>
        </div>
        
        <div class="section">
            <h2>MCP Servers</h2>
            <div class="info">
                Configure Model Context Protocol (MCP) servers for extended functionality.
            </div>
            
            <div class="form-group">
                <label for="mcpServers">MCP Servers Configuration (JSON):</label>
                <textarea id="mcpServers" placeholder='{"mcp_servers": {}}'></textarea>
            </div>
            
            <button onclick="saveMCPServers()">Save MCP Servers</button>
        </div>
        
        <div class="section">
            <h2>Configuration Files</h2>
            <div class="info">
                Configuration file locations on your system.
            </div>
            
            <div class="path-info">
                <strong>Config Directory:</strong> <span id="configDir"></span><br>
                <strong>Config File:</strong> <span id="configFile"></span><br>
                <strong>MCP Servers File:</strong> <span id="mcpServersFile"></span>
            </div>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        let currentConfig = {};
        let currentMCPServers = {};

        function saveBasicConfig() {
            const config = {
                puppy_name: document.getElementById('puppyName').value,
                owner_name: document.getElementById('ownerName').value,
                model: document.getElementById('model').value,
                message_history_limit: document.getElementById('messageHistoryLimit').value,
                yolo_mode: document.getElementById('yoloMode').value
            };

            vscode.postMessage({
                type: 'saveConfig',
                data: config
            });
        }

        function saveMCPServers() {
            try {
                const mcpServersText = document.getElementById('mcpServers').value;
                const mcpServers = JSON.parse(mcpServersText);
                
                vscode.postMessage({
                    type: 'saveMCPServers',
                    data: mcpServers
                });
            } catch (error) {
                alert('Invalid JSON format in MCP Servers configuration');
            }
        }

        function loadConfigData(data) {
            const { config, mcpServers, paths } = data;
            
            // Load basic config
            document.getElementById('puppyName').value = config.puppy_name || '';
            document.getElementById('ownerName').value = config.owner_name || '';
            document.getElementById('model').value = config.model || 'gpt-4.1';
            document.getElementById('messageHistoryLimit').value = config.message_history_limit || '40';
            document.getElementById('yoloMode').value = config.yolo_mode || '';
            
            // Load MCP servers
            document.getElementById('mcpServers').value = JSON.stringify(mcpServers, null, 2);
            
            // Load paths
            document.getElementById('configDir').textContent = paths.configDir;
            document.getElementById('configFile').textContent = paths.configFile;
            document.getElementById('mcpServersFile').textContent = paths.mcpServersFile;
            
            currentConfig = config;
            currentMCPServers = mcpServers;
        }

        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'configLoaded':
                    loadConfigData(message.data);
                    break;
            }
        });

        // Request initial config load
        vscode.postMessage({ type: 'loadConfig' });
    </script>
</body>
</html>`;
    }

    public dispose() {
        ConfigPanel.currentPanel = undefined;

        this._panel.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
