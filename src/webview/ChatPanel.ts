import * as vscode from 'vscode';
import { ChatMessage, WebviewMessage } from '../types';
import { ConfigManager } from '../config/ConfigManager';
import { PuppyProcessManager } from '../services/PuppyProcessManager';

export class ChatPanel {
    public static currentPanel: ChatPanel | undefined;
    private readonly _panel: vscode.WebviewPanel;
    private readonly _extensionUri: vscode.Uri;
    private _disposables: vscode.Disposable[] = [];
    private _messages: ChatMessage[] = [];
    private _configManager: ConfigManager;
    private _processManager: PuppyProcessManager;

    public static createOrShow(extensionUri: vscode.Uri) {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (ChatPanel.currentPanel) {
            ChatPanel.currentPanel._panel.reveal(column);
            return;
        }

        const panel = vscode.window.createWebviewPanel(
            'codePuppyChat',
            'Code Puppy Chat',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [
                    vscode.Uri.joinPath(extensionUri, 'media'),
                    vscode.Uri.joinPath(extensionUri, 'out', 'webview')
                ]
            }
        );

        ChatPanel.currentPanel = new ChatPanel(panel, extensionUri);
    }

    private constructor(panel: vscode.WebviewPanel, extensionUri: vscode.Uri) {
        this._panel = panel;
        this._extensionUri = extensionUri;
        this._configManager = ConfigManager.getInstance();
        this._processManager = PuppyProcessManager.getInstance();

        this._update();
        this._panel.onDidDispose(() => this.dispose(), null, this._disposables);

        this._panel.webview.onDidReceiveMessage(
            async (message: WebviewMessage) => {
                switch (message.type) {
                    case 'chat':
                        await this._handleChatMessage(message.data);
                        break;
                    case 'config':
                        await this._handleConfigMessage(message.data);
                        break;
                    default:
                        break;
                }
            },
            null,
            this._disposables
        );
    }

    private async _handleChatMessage(data: any) {
        const { content } = data;
        
        if (!content?.trim()) {
            return;
        }

        // Add user message
        const userMessage: ChatMessage = {
            id: this._generateId(),
            role: 'user',
            content: content.trim(),
            timestamp: new Date()
        };
        
        this._messages.push(userMessage);
        this._updateMessages();

        try {
            // Check if it's a meta command
            if (content.trim().startsWith('~')) {
                await this._handleMetaCommand(content.trim());
                return;
            }

            // Send to Code Puppy
            const response = await this._processManager.executeCommand(content.trim());
            
            // Add assistant response
            const assistantMessage: ChatMessage = {
                id: this._generateId(),
                role: 'assistant',
                content: response,
                timestamp: new Date()
            };
            
            this._messages.push(assistantMessage);
            this._updateMessages();

        } catch (error) {
            const errorMessage: ChatMessage = {
                id: this._generateId(),
                role: 'assistant',
                content: `Error: ${error instanceof Error ? error.message : String(error)}`,
                timestamp: new Date(),
                isError: true
            };
            
            this._messages.push(errorMessage);
            this._updateMessages();
        }
    }

    private async _handleMetaCommand(command: string) {
        try {
            let response = '';

            if (command === '~show') {
                const config = await this._configManager.getAllConfig();
                const puppyName = await this._configManager.getPuppyName();
                const ownerName = await this._configManager.getOwnerName();
                const model = await this._configManager.getCurrentModel();
                
                response = `🐶 Puppy Status\n` +
                          `Puppy Name: ${puppyName}\n` +
                          `Owner: ${ownerName}\n` +
                          `Model: ${model}\n` +
                          `Config: ${JSON.stringify(config, null, 2)}`;

            } else if (command.startsWith('~set ')) {
                const argStr = command.substring(5).trim();
                let key: string, value: string;
                
                if (argStr.includes('=')) {
                    [key, value] = argStr.split('=', 2);
                    key = key.trim();
                    value = value.trim();
                } else {
                    const parts = argStr.split(' ', 2);
                    key = parts[0];
                    value = parts[1] || '';
                }
                
                if (key) {
                    await this._configManager.setConfigValue(key, value);
                    response = `🌶 Set ${key} = "${value}" in puppy.cfg!`;
                } else {
                    response = 'Usage: ~set KEY=VALUE or ~set KEY VALUE';
                }

            } else if (command.startsWith('~m ')) {
                const model = command.substring(3).trim();
                if (model) {
                    await this._configManager.setConfigValue('model', model);
                    response = `🔄 Switched to model: ${model}`;
                } else {
                    response = 'Usage: ~m <model-name>';
                }

            } else if (command === '~clear') {
                this._messages = [];
                this._updateMessages();
                response = 'Conversation history cleared!';

            } else if (command === '~help' || command === '~h') {
                response = `Meta Commands Help\n` +
                          `~help, ~h             Show this help message\n` +
                          `~show                 Show puppy config key-values\n` +
                          `~set                  Set puppy config key-values\n` +
                          `~m <model>            Set active model\n` +
                          `~clear                Clear chat history`;

            } else {
                response = `Unknown meta command: ${command}\nType ~help for options.`;
            }

            const assistantMessage: ChatMessage = {
                id: this._generateId(),
                role: 'assistant',
                content: response,
                timestamp: new Date()
            };
            
            this._messages.push(assistantMessage);
            this._updateMessages();

        } catch (error) {
            const errorMessage: ChatMessage = {
                id: this._generateId(),
                role: 'assistant',
                content: `Error executing meta command: ${error instanceof Error ? error.message : String(error)}`,
                timestamp: new Date(),
                isError: true
            };
            
            this._messages.push(errorMessage);
            this._updateMessages();
        }
    }

    private async _handleConfigMessage(data: any) {
        // Handle configuration-related messages
        const { action } = data;
        
        switch (action) {
            case 'getConfig':
                const config = await this._configManager.getAllConfig();
                this._panel.webview.postMessage({
                    type: 'config',
                    data: { action: 'configData', config }
                });
                break;
        }
    }

    private _updateMessages() {
        this._panel.webview.postMessage({
            type: 'chat',
            data: { action: 'updateMessages', messages: this._messages }
        });
    }

    private _generateId(): string {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    public addMessage(message: ChatMessage) {
        this._messages.push(message);
        this._updateMessages();
    }

    public clearMessages() {
        this._messages = [];
        this._updateMessages();
    }

    private async _update() {
        const webview = this._panel.webview;
        this._panel.webview.html = await this._getHtmlForWebview(webview);
    }

    private async _getHtmlForWebview(webview: vscode.Webview): Promise<string> {
        const puppyName = await this._configManager.getPuppyName();
        const currentModel = await this._configManager.getCurrentModel();

        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Puppy Chat</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            margin: 0;
            padding: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            padding: 10px 15px;
            background-color: var(--vscode-panel-background);
            border-bottom: 1px solid var(--vscode-panel-border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h2 {
            margin: 0;
            color: var(--vscode-foreground);
        }
        
        .model-info {
            font-size: 0.9em;
            color: var(--vscode-descriptionForeground);
        }
        
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        
        .message {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            max-width: 80%;
        }
        
        .message.user {
            background-color: var(--vscode-inputValidation-infoBorder);
            margin-left: auto;
            text-align: right;
        }
        
        .message.assistant {
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
        }
        
        .message.error {
            background-color: var(--vscode-inputValidation-errorBackground);
            border: 1px solid var(--vscode-inputValidation-errorBorder);
        }
        
        .message-content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .message-time {
            font-size: 0.8em;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }
        
        .input-container {
            padding: 15px;
            background-color: var(--vscode-panel-background);
            border-top: 1px solid var(--vscode-panel-border);
            display: flex;
            gap: 10px;
        }
        
        .input-field {
            flex: 1;
            padding: 8px 12px;
            background-color: var(--vscode-input-background);
            border: 1px solid var(--vscode-input-border);
            color: var(--vscode-input-foreground);
            border-radius: 4px;
            font-family: inherit;
            font-size: inherit;
        }
        
        .input-field:focus {
            outline: none;
            border-color: var(--vscode-focusBorder);
        }
        
        .send-button {
            padding: 8px 16px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
        }
        
        .send-button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="header">
        <h2>🐶 ${puppyName}</h2>
        <div class="model-info">Model: ${currentModel}</div>
    </div>
    
    <div class="chat-container" id="chatContainer">
        <!-- Messages will be inserted here -->
    </div>
    
    <div class="input-container">
        <input type="text" id="messageInput" class="input-field" placeholder="Enter your coding task or ~help for commands..." />
        <button id="sendButton" class="send-button">Send</button>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');

        function sendMessage() {
            const content = messageInput.value.trim();
            if (!content) return;

            vscode.postMessage({
                type: 'chat',
                data: { content }
            });

            messageInput.value = '';
        }

        function updateMessages(messages) {
            chatContainer.innerHTML = '';
            messages.forEach(message => {
                const messageDiv = document.createElement('div');
                messageDiv.className = \`message \${message.role}\${message.isError ? ' error' : ''}\`;
                
                const contentDiv = document.createElement('div');
                contentDiv.className = 'message-content';
                contentDiv.textContent = message.content;
                
                const timeDiv = document.createElement('div');
                timeDiv.className = 'message-time';
                timeDiv.textContent = new Date(message.timestamp).toLocaleTimeString();
                
                messageDiv.appendChild(contentDiv);
                messageDiv.appendChild(timeDiv);
                chatContainer.appendChild(messageDiv);
            });
            
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        sendButton.addEventListener('click', sendMessage);
        messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        window.addEventListener('message', event => {
            const message = event.data;
            switch (message.type) {
                case 'chat':
                    if (message.data.action === 'updateMessages') {
                        updateMessages(message.data.messages);
                    }
                    break;
            }
        });

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>`;
    }

    public dispose() {
        ChatPanel.currentPanel = undefined;

        this._panel.dispose();

        while (this._disposables.length) {
            const x = this._disposables.pop();
            if (x) {
                x.dispose();
            }
        }
    }
}
