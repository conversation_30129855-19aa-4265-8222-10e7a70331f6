import * as vscode from 'vscode';
import { <PERSON>Hand<PERSON> } from './commands/CommandHandler';
import { StatusBarManager } from './ui/StatusBarManager';
import { ConfigManager } from './config/ConfigManager';
import { PuppyProcessManager } from './services/PuppyProcessManager';
import { ChatPanel } from './webview/ChatPanel';

let commandHandler: CommandHandler;
let statusBarManager: StatusBarManager;
let configManager: ConfigManager;
let processManager: PuppyProcessManager;

/**
 * This method is called when your extension is activated
 */
export async function activate(context: vscode.ExtensionContext) {
    console.log('🐶 Code Puppy extension is activating...');

    try {
        // Initialize managers
        configManager = ConfigManager.getInstance();
        processManager = PuppyProcessManager.getInstance();
        statusBarManager = StatusBarManager.getInstance();
        commandHandler = new CommandHandler();

        // Register commands
        commandHandler.registerCommands(context);

        // Initialize status bar
        await statusBarManager.initialize();

        // Check if Code Puppy is available
        const isAvailable = await commandHandler.checkAvailability();

        if (isAvailable) {
            // Initialize configuration if needed
            await commandHandler.initializeIfNeeded();

            // Auto-show chat if configured
            const config = vscode.workspace.getConfiguration('codePuppy');
            const autoShowChat = config.get<boolean>('autoShowChat', true);

            if (autoShowChat) {
                // Delay showing chat to avoid interfering with startup
                setTimeout(() => {
                    ChatPanel.createOrShow(context.extensionUri);
                }, 1000);
            }

            console.log('🐶 Code Puppy extension activated successfully!');
            vscode.window.showInformationMessage('🐶 Code Puppy is ready to help!');
        } else {
            console.log('⚠️ Code Puppy is not available');
            statusBarManager.showError('Not Available');
        }

        // Listen for configuration changes
        context.subscriptions.push(
            vscode.workspace.onDidChangeConfiguration(async (event) => {
                if (event.affectsConfiguration('codePuppy')) {
                    await statusBarManager.updateStatusBar();
                }
            })
        );

        // Dispose resources when extension is deactivated
        context.subscriptions.push({
            dispose: () => {
                statusBarManager.dispose();
                processManager.dispose();
            }
        });

    } catch (error) {
        console.error('Error activating Code Puppy extension:', error);
        vscode.window.showErrorMessage(`Failed to activate Code Puppy: ${error instanceof Error ? error.message : String(error)}`);
    }
}

/**
 * This method is called when your extension is deactivated
 */
export function deactivate() {
    console.log('🐶 Code Puppy extension is deactivating...');

    if (statusBarManager) {
        statusBarManager.dispose();
    }

    if (processManager) {
        processManager.dispose();
    }

    console.log('🐶 Code Puppy extension deactivated');
}
