import * as vscode from 'vscode';
import * as cp from 'child_process';
import * as path from 'path';
import { PuppyProcess, AgentResponse } from '../types';

export class PuppyProcessManager {
    private static instance: PuppyProcessManager;
    private activeProcess?: PuppyProcess;
    private outputChannel: vscode.OutputChannel;

    private constructor() {
        this.outputChannel = vscode.window.createOutputChannel('Code Puppy');
    }

    public static getInstance(): PuppyProcessManager {
        if (!PuppyProcessManager.instance) {
            PuppyProcessManager.instance = new PuppyProcessManager();
        }
        return PuppyProcessManager.instance;
    }

    /**
     * Get Python path from configuration
     */
    private getPythonPath(): string {
        const config = vscode.workspace.getConfiguration('codePuppy');
        return config.get<string>('pythonPath', 'python');
    }

    /**
     * Get Code Puppy path from configuration or detect it
     */
    private async getCodePuppyPath(): Promise<string> {
        const config = vscode.workspace.getConfiguration('codePuppy');
        const configuredPath = config.get<string>('codePuppyPath', '');
        
        if (configuredPath) {
            return configuredPath;
        }

        // Try to detect code_puppy installation
        // First check if it's in the current workspace
        const workspaceFolders = vscode.workspace.workspaceFolders;
        if (workspaceFolders) {
            for (const folder of workspaceFolders) {
                const codePuppyPath = path.join(folder.uri.fsPath, 'code_puppy');
                try {
                    // Check if code_puppy directory exists
                    const stat = await vscode.workspace.fs.stat(vscode.Uri.file(codePuppyPath));
                    if (stat.type === vscode.FileType.Directory) {
                        return codePuppyPath;
                    }
                } catch {
                    // Directory doesn't exist, continue
                }
            }
        }

        // If not found in workspace, assume it's installed globally
        return 'code_puppy';
    }

    /**
     * Execute a single command
     */
    public async executeCommand(command: string): Promise<string> {
        return new Promise(async (resolve, reject) => {
            try {
                const pythonPath = this.getPythonPath();
                const codePuppyPath = await this.getCodePuppyPath();
                
                this.outputChannel.appendLine(`Executing: ${command}`);
                
                const args = ['-m', 'code_puppy.main'];
                
                // If codePuppyPath is a directory (local installation), run from that directory
                let cwd: string = process.cwd();

                if (codePuppyPath !== 'code_puppy') {
                    // Local installation
                    cwd = codePuppyPath;
                    args[1] = 'code_puppy.main';
                }

                // Add the command arguments
                args.push(...command.split(' '));

                const childProcess = cp.spawn(pythonPath, args, {
                    cwd,
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                let output = '';
                let errorOutput = '';

                childProcess.stdout?.on('data', (data: Buffer) => {
                    const text = data.toString();
                    output += text;
                    this.outputChannel.append(text);
                });

                childProcess.stderr?.on('data', (data: Buffer) => {
                    const text = data.toString();
                    errorOutput += text;
                    this.outputChannel.append(`ERROR: ${text}`);
                });

                childProcess.on('close', (code: number | null) => {
                    if (code === 0) {
                        resolve(output);
                    } else {
                        reject(new Error(`Process exited with code ${code}: ${errorOutput}`));
                    }
                });

                childProcess.on('error', (error: Error) => {
                    this.outputChannel.appendLine(`Process error: ${error.message}`);
                    reject(error);
                });

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Start interactive mode
     */
    public async startInteractive(): Promise<PuppyProcess> {
        if (this.activeProcess?.isRunning) {
            throw new Error('Interactive process is already running');
        }

        return new Promise(async (resolve, reject) => {
            try {
                const pythonPath = this.getPythonPath();
                const codePuppyPath = await this.getCodePuppyPath();
                
                this.outputChannel.appendLine('Starting interactive mode...');
                
                const args = ['-m', 'code_puppy.main', '--interactive'];
                
                let cwd: string = process.cwd();
                if (codePuppyPath !== 'code_puppy') {
                    cwd = codePuppyPath;
                    args[1] = 'code_puppy.main';
                }

                const childProcess = cp.spawn(pythonPath, args, {
                    cwd,
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                const puppyProcess: PuppyProcess = {
                    process: childProcess,
                    isRunning: true,
                    isInteractive: true
                };

                childProcess.stdout?.on('data', (data: Buffer) => {
                    const text = data.toString();
                    this.outputChannel.append(text);
                    if (puppyProcess.onOutput) {
                        puppyProcess.onOutput(text);
                    }
                });

                childProcess.stderr?.on('data', (data: Buffer) => {
                    const text = data.toString();
                    this.outputChannel.append(`ERROR: ${text}`);
                    if (puppyProcess.onError) {
                        puppyProcess.onError(text);
                    }
                });

                childProcess.on('close', (code: number | null) => {
                    puppyProcess.isRunning = false;
                    this.outputChannel.appendLine(`Interactive process exited with code ${code}`);
                    if (puppyProcess.onExit) {
                        puppyProcess.onExit(code || 0);
                    }
                    if (this.activeProcess === puppyProcess) {
                        this.activeProcess = undefined;
                    }
                });

                childProcess.on('error', (error: Error) => {
                    puppyProcess.isRunning = false;
                    this.outputChannel.appendLine(`Process error: ${error.message}`);
                    if (puppyProcess.onError) {
                        puppyProcess.onError(error.message);
                    }
                    reject(error);
                });

                this.activeProcess = puppyProcess;
                resolve(puppyProcess);

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Send input to interactive process
     */
    public sendInput(input: string): void {
        if (!this.activeProcess?.isRunning) {
            throw new Error('No active interactive process');
        }

        this.outputChannel.appendLine(`Sending: ${input}`);
        this.activeProcess.process.stdin?.write(input + '\n');
    }

    /**
     * Stop the active process
     */
    public stopProcess(): void {
        if (this.activeProcess?.isRunning) {
            this.outputChannel.appendLine('Stopping process...');
            this.activeProcess.process.kill();
            this.activeProcess.isRunning = false;
            this.activeProcess = undefined;
        }
    }

    /**
     * Get the active process
     */
    public getActiveProcess(): PuppyProcess | undefined {
        return this.activeProcess;
    }

    /**
     * Check if Code Puppy is available
     */
    public async checkAvailability(): Promise<boolean> {
        try {
            const output = await this.executeCommand('--help');
            return output.includes('Code Puppy');
        } catch {
            return false;
        }
    }

    /**
     * Get output channel for debugging
     */
    public getOutputChannel(): vscode.OutputChannel {
        return this.outputChannel;
    }

    /**
     * Dispose resources
     */
    public dispose(): void {
        this.stopProcess();
        this.outputChannel.dispose();
    }
}
