/**
 * Type definitions for Code Puppy VSCode Extension
 */

export interface PuppyConfig {
  puppy_name?: string;
  owner_name?: string;
  model?: string;
  yolo_mode?: string;
  message_history_limit?: string;
  [key: string]: string | undefined;
}

export interface MCPServer {
  command: string;
  args?: string[];
  env?: Record<string, string>;
}

export interface MCPServersConfig {
  mcp_servers: Record<string, MCPServer | string>;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  isError?: boolean;
}

export interface AgentResponse {
  output_message: string;
  awaiting_user_input: boolean;
}

export interface PuppyProcess {
  process: any;
  isRunning: boolean;
  isInteractive: boolean;
  onOutput?: (data: string) => void;
  onError?: (error: string) => void;
  onExit?: (code: number) => void;
}

export interface ModelInfo {
  name: string;
  displayName: string;
  provider: string;
}

export interface CodePuppyExtensionState {
  isActive: boolean;
  currentModel: string;
  puppyName: string;
  ownerName: string;
  chatHistory: ChatMessage[];
  activeProcess?: PuppyProcess;
}

export interface ConfigurationItem {
  key: string;
  value: string;
  description?: string;
  type: 'string' | 'number' | 'boolean';
}

export interface WebviewMessage {
  type: 'chat' | 'config' | 'status' | 'error';
  data: any;
}
